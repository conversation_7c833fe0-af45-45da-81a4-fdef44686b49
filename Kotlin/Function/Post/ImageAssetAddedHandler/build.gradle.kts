import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar

plugins {
    id("hero.cloud-function-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Function:Subscriber"))
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Contract"))

    implementation("com.twelvemonkeys.imageio:imageio-webp:_")
}

tasks.withType<ShadowJar> {
    minimize {
        exclude(dependency("com.twelvemonkeys.imageio:.*:.*"))
    }
}
