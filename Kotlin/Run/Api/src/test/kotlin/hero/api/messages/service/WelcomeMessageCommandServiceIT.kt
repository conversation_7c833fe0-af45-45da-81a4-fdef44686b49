package hero.api.messages.service

import hero.api.gjirafaAsset
import hero.api.post.service.dto.GjirafaAssetInput
import hero.api.post.service.dto.ImageAssetInput
import hero.api.post.service.dto.PostAssetInput
import hero.gjirafa.GjirafaUploadsService
import hero.model.DocumentAsset
import hero.model.DocumentType
import hero.model.ImageAsset
import hero.model.PostAsset
import hero.model.topics.PostState
import hero.test.IntegrationTest
import hero.test.IntegrationTestHelper.TestCollections
import hero.test.TestRepositories
import hero.test.time.TestClock
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class WelcomeMessageCommandServiceIT : IntegrationTest() {
    private val expectedTimestamp = Instant.parse("2023-09-17T00:00:00Z")

    @Nested
    inner class CreateOrGetWelcomeMessage {
        @Test
        fun `should create welcome message if it does not exist yet`() {
            val underTest = prepareService()

            testHelper.createUser("cestmir")

            val result = underTest.execute(CreateOrGetWelcomeMessage("cestmir"))

            assertThat(result.text).isBlank
            assertThat(result.state).isEqualTo(PostState.REVISION)
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.messageThreadId).isNull()
            assertThat(result.parentId).isNull()
            assertThat(result.published).isEqualTo(expectedTimestamp)
            assertThat(TestCollections.postsCollection[welcomeMessageId("cestmir")].get()).isEqualTo(result)
        }

        @Test
        fun `should return existing welcome message`() {
            val underTest = prepareService()

            testHelper.createUser("cestmir")
            testHelper.createPost(
                "cestmir",
                id = welcomeMessageId("cestmir"),
                text = "Hello world",
                state = PostState.REVISION,
            )

            val result = underTest.execute(CreateOrGetWelcomeMessage("cestmir"))

            assertThat(result.text).isEqualTo("Hello world")
            assertThat(result.state).isEqualTo(PostState.REVISION)
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.messageThreadId).isNull()
            assertThat(result.parentId).isNull()
        }
    }

    @Nested
    inner class UpdateWelcomeMessage {
        @Test
        fun `should update welcome message`() {
            val gjirafaUploadsService = mockk<GjirafaUploadsService>()
            val gjirafaAsset = gjirafaAsset("id")
            every { gjirafaUploadsService.getAsset(any(), any(), any()) } returns gjirafaAsset
            val underTest = prepareService(gjirafaUploadsService)

            testHelper.createUser("cestmir")
            testHelper.createPost(
                "cestmir",
                id = welcomeMessageId("cestmir"),
                text = "Hello world",
                state = PostState.REVISION,
            )

            val inputs = listOf(
                PostAssetInput(image = ImageAssetInput("https://image.com", 100, 500, "fileName", 150)),
                PostAssetInput(gjirafa = GjirafaAssetInput("id")),
                PostAssetInput(document = DocumentAsset("document-url", DocumentType.DOCX, "name", 100)),
            )
            val result = underTest.execute(UpdateWelcomeMessage("cestmir", "new text", inputs))

            assertThat(result.text).isEqualTo("new text")
            assertThat(result.state).isEqualTo(PostState.REVISION)
            assertThat(result.userId).isEqualTo("cestmir")
            assertThat(result.assets).containsExactly(
                PostAsset(image = ImageAsset.of("https://image.com", 100, 500, "fileName", 150)),
                PostAsset(gjirafa = gjirafaAsset),
                PostAsset(document = DocumentAsset("document-url", DocumentType.DOCX, "name", 100)),
            )
            assertThat(result.messageThreadId).isNull()
            assertThat(result.parentId).isNull()
        }
    }

    private fun prepareService(gjirafaUploadsService: GjirafaUploadsService = mockk()): WelcomeMessageCommandService {
        val testClock = TestClock(expectedTimestamp)
        return WelcomeMessageCommandService(
            TestCollections.postsCollection,
            TestRepositories.postRepository,
            gjirafaUploadsService,
            testClock,
        )
    }
}
