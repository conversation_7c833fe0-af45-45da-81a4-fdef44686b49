package hero.api.messages.service

import hero.api.post.service.dto.ImageAssetInput
import hero.api.post.service.dto.PostAssetInput
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gjirafa.GjirafaUploadsService
import hero.model.ImageAsset
import hero.model.Post
import hero.model.PostAsset
import hero.model.topics.PostState
import hero.repository.post.PostRepository
import java.time.Clock
import java.time.Instant

class WelcomeMessageCommandService(
    private val postsCollection: TypedCollectionReference<Post>,
    private val postRepository: PostRepository,
    private val gjirafaService: GjirafaUploadsService,
    private val clock: Clock = Clock.systemUTC(),
) {
    fun execute(command: CreateOrGetWelcomeMessage): Post {
        val welcomeMessageId = welcomeMessageId(command.userId)
        val welcomeMessage = postRepository.findById(welcomeMessageId)
        if (welcomeMessage != null) {
            return welcomeMessage
        }

        return Post(
            userId = command.userId,
            state = PostState.REVISION,
            id = welcomeMessageId,
            published = Instant.now(clock),
            created = Instant.now(clock),
            updated = Instant.now(clock),
            text = "",
        ).also {
            postsCollection[welcomeMessageId].set(it)
            postRepository.save(it)
        }
    }

    fun execute(command: UpdateWelcomeMessage): Post {
        val welcomeMessage = postRepository.getById(welcomeMessageId(command.userId))

        if (welcomeMessage.userId != command.userId) {
            throw ForbiddenException("Cannot update welcome message of another user")
        }

        val updatedWelcomeMessage = welcomeMessage.copy(
            text = command.text,
            assets = command.assets
                .map { processAsset(it, command.userId) }
                .filter { !it.isEmpty() },
        )
        return postRepository.save(updatedWelcomeMessage)
    }

    private fun processAsset(
        assetDto: PostAssetInput,
        creatorId: String,
    ): PostAsset =
        PostAsset(
            image = assetDto.image?.toImageAsset(),
            gjirafa = assetDto.gjirafa?.let { gjirafaService.getAsset(userId = creatorId, assetId = it.id) },
            document = assetDto.document,
        )

    private fun ImageAssetInput.toImageAsset() =
        ImageAsset.of(id = url, width = width, height = height, fileName = fileName, fileSize = fileSize)
}

data class CreateOrGetWelcomeMessage(val userId: String)

data class UpdateWelcomeMessage(val userId: String, val text: String, val assets: List<PostAssetInput>)

fun welcomeMessageId(userId: String) = "welcome-message-$userId"
