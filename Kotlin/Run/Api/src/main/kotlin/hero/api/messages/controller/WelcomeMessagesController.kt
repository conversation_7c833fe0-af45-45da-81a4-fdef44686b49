package hero.api.messages.controller

import hero.api.messages.controller.dto.UpdateWelcomeMessageRequest
import hero.api.messages.controller.dto.exampleUpdateWelcomeMessageRequest
import hero.api.messages.service.CreateOrGetWelcomeMessage
import hero.api.messages.service.UpdateWelcomeMessage
import hero.api.messages.service.WelcomeMessageCommandService
import hero.api.post.controller.dto.PostRenderMeta
import hero.api.post.controller.dto.examplePostResponse
import hero.api.post.controller.dto.toResponse
import hero.http4k.auth.getJwtUser
import hero.http4k.extensions.body
import hero.http4k.extensions.example
import hero.http4k.extensions.get
import hero.http4k.extensions.lens
import hero.http4k.extensions.put
import org.http4k.contract.ContractRoute
import org.http4k.contract.div
import org.http4k.core.Response
import org.http4k.core.Status
import org.http4k.lens.Path

class WelcomeMessagesController(
    private val welcomeMessageCommandService: WelcomeMessageCommandService,
) {
    @Suppress("Unused")
    val routeGetWelcomeMessage: ContractRoute =
        ("/v1/users" / Path.of("userId") / "welcome-messages").get(
            summary = "Get user's welcome message",
            tag = "Message threads",
            parameters = object {},
            responses = listOf(Status.OK example examplePostResponse),
            handler = { request, _, userId, _ ->
                val user = request.getJwtUser()
                require(userId == user.id) { "Path userId must match authenticated user" }
                val result = welcomeMessageCommandService.execute(CreateOrGetWelcomeMessage(user.id))

                Response(Status.OK).body(result.toResponse(PostRenderMeta(true), null))
            },
        )

    @Suppress("Unused")
    val routeUpdateWelcomeMessage: ContractRoute =
        ("/v1/users" / Path.of("userId") / "welcome-messages").put(
            summary = "Get user's welcome message",
            tag = "Message threads",
            parameters = object {},
            responses = listOf(Status.OK example examplePostResponse),
            receiving = exampleUpdateWelcomeMessageRequest,
            handler = { request, _, userId, _ ->
                val user = request.getJwtUser()
                val body = lens<UpdateWelcomeMessageRequest>(request)
                require(userId == user.id) { "Path userId must match authenticated user" }
                val result = welcomeMessageCommandService.execute(
                    UpdateWelcomeMessage(
                        user.id,
                        body.text,
                        body.assets,
                    ),
                )

                Response(Status.OK).body(result.toResponse(PostRenderMeta(true), null))
            },
        )
}
