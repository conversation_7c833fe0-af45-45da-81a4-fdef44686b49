package hero.api.community.service

import hero.api.user.repository.pathUpdateableAfter
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ConflictException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.PubSub
import hero.model.Community
import hero.model.ImageAsset
import hero.model.topics.CommunityCreated
import hero.repository.community.CommunityRepository
import hero.repository.user.UserRepository
import hero.sql.jooq.Tables
import org.jooq.DSLContext
import java.time.Clock
import java.time.Instant
import java.util.UUID

class CommunityCommandService(
    private val communityRepository: CommunityRepository,
    private val userRepository: UserRepository,
    private val pubSub: PubSub,
    lazyContext: Lazy<DSLContext>,
    private val clock: Clock = Clock.systemUTC(),
) {
    val context: DSLContext by lazyContext

    fun execute(command: CreateCommunity): Community {
        val creator = userRepository.getById(command.userId)

        if (communityRepository.findByOwnerId(command.userId).isNotEmpty()) {
            throw ConflictException("User ${command.userId} already has a community")
        }

        val now = Instant.now(clock)
        val community = Community(
            id = UUID.randomUUID(),
            name = creator.name,
            description = "Community",
            slug = creator.path,
            ownerId = command.userId,
            membersCount = 1,
            image = creator.image,
            createdAt = now,
            updatedAt = now,
            deletedAt = null,
            threadsCount = 0,
            slugUpdatedAt = null,
        )

        communityRepository.save(community)
        pubSub.publish(CommunityCreated(community.id))
        context
            .update(Tables.USER)
            .set(Tables.USER.OWNED_COMMUNITIES_COUNT, Tables.USER.OWNED_COMMUNITIES_COUNT.plus(1))
            .where(Tables.USER.ID.eq(command.userId))
            .execute()

        return community
    }

    fun execute(command: UpdateCommunity): Community {
        validateSlug(command.slug)
        val community = communityRepository.getById(command.communityId)
        val owner = userRepository.getById(community.ownerId)

        if (command.userId != community.ownerId) {
            throw ForbiddenException("User ${command.userId} cannot update community ${command.communityId}")
        }

        val slugUpdatedAt = if (command.slug != community.slug) {
            val communityWithSlug = context
                .selectFrom(Tables.COMMUNITY)
                .where(Tables.COMMUNITY.SLUG.eq(command.slug))
                .fetchOne()

            if (communityWithSlug != null) {
                throw ConflictException("Community with slug ${command.slug} already exists")
            }

            if (command.slug != owner.path) {
                val userWithSlug = context.selectFrom(Tables.USER)
                    .where(Tables.USER.PATH.eq(command.slug))
                    .fetch()

                if (userWithSlug.isNotEmpty) {
                    throw ConflictException("User with slug ${command.slug} already exists")
                }
            }

            if (community.slugUpdatedAt?.pathUpdateableAfter()?.isAfter(Instant.now()) == true) {
                throw ConflictException("Community slug can be changed once in an hour")
            }

            Instant.now(clock)
        } else {
            community.slugUpdatedAt
        }

        val updatedCommunity = community.copy(
            name = command.name,
            description = command.description,
            image = command.image,
            slug = command.slug,
            updatedAt = Instant.now(clock),
            slugUpdatedAt = slugUpdatedAt,
        )
        communityRepository.save(updatedCommunity)

        return updatedCommunity
    }

    private fun validateSlug(slug: String) {
        if (slug.length < 3) {
            throw BadRequestException("Slug must be at least 3 characters long")
        }

        if (!slug.matches("[a-z0-9]+".toRegex())) {
            throw BadRequestException("Slug must contain only lowercase letters and numbers")
        }
    }
}

data class CreateCommunity(val userId: String)

data class UpdateCommunity(
    val communityId: UUID,
    val userId: String,
    val name: String,
    val description: String,
    val image: ImageAsset?,
    val slug: String,
)
