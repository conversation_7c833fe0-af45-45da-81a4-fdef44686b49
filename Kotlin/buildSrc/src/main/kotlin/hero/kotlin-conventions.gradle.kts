package hero

import Kotlin
import KotlinX
import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import com.github.jengelman.gradle.plugins.shadow.transformers.Log4j2PluginsCacheFileTransformer
import io.gitlab.arturbosch.detekt.Detekt
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.noarg.gradle.NoArgExtension

plugins {
    base
    kotlin apply false
    `maven-publish`
    id("org.jlleitschuh.gradle.ktlint")
    id("com.gradleup.shadow")
    id("org.jetbrains.kotlin.plugin.noarg")
    id("io.gitlab.arturbosch.detekt")
    id("io.sentry.jvm.gradle")
}

fun projectModule(module: String): Any =
    if (project.hasProperty("ci"))
        "hero:${module.lowercase().replace(".*:".toRegex(), "")}:ci"
    else
        project(module)

val projectModule by extra { projectName: String -> projectModule(projectName) }

private val jvmVersion = "21"
private val jvmCompilerVersion = JvmTarget.JVM_21

group = "hero"

repositories {
    maven(url = "${rootProject.rootDir}/../m2")
    mavenCentral()
    maven(url = "https://jitpack.io")
    maven(url = "https://plugins.gradle.org/m2/")
}

kotlin {
    jvmToolchain {
        this.languageVersion.set(JavaLanguageVersion.of(jvmVersion))
    }
    compilerOptions {
        jvmTarget = jvmCompilerVersion
    }
}

tasks.compileKotlin {
    compilerOptions {
        jvmTarget = jvmCompilerVersion
    }
}

tasks.compileTestKotlin {
    compilerOptions {
        jvmTarget = jvmCompilerVersion
    }
}

private val coreModulePath = ":Modules:Core"
dependencies {
    implementation("org.jetbrains.kotlin:kotlin-reflect:_")
    implementation(Kotlin.stdlib)
    implementation(Kotlin.stdlib.jdk8)
    implementation(KotlinX.coroutines.core)
    implementation("org.apache.logging.log4j:log4j-api:_")
    implementation("org.apache.logging.log4j:log4j-core:_")
    implementation("org.apache.logging.log4j:log4j-slf4j2-impl:_")
    implementation("org.apache.logging.log4j:log4j-web:_")

    detektPlugins("com.gitlab.cromefire:detekt-gitlab-report:_")

    if (project.path != coreModulePath) {
        detektPlugins(projectModule(":Modules:Core"))
        implementation(projectModule(coreModulePath))
    }
}

configure<NoArgExtension> {
    annotation("hero.core.annotation.NoArg")
    invokeInitializers = true
}

configure<PublishingExtension> {
    publications {
        create<MavenPublication>(project.name) {
            groupId = "hero"
            artifactId = project.name.lowercase()
            version = "ci"
            from(components["kotlin"])
        }
    }

    repositories {
        maven {
            url = uri("file://${rootProject.rootDir}/../m2") // /${project.name}
        }
    }
}

tasks.test {
    useJUnitPlatform()
    testLogging {
        events("passed", "skipped", "failed")
        showStackTraces = true
        showStandardStreams = true
        exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
    }
    // https://github.com/mockk/mockk/issues/681
    jvmArgs(
        // needed for mocking Instant and ZonedDateTime
        "--add-opens",
        "java.base/java.time=ALL-UNNAMED",
        // needed for HttpUrlConnection to set `Origin` header (used in media uploads)
        "-Dsun.net.http.allowRestrictedHeaders=true",
    )

    // Required, otherwise static mocks might be failing. Also make sure to call @AfterEach[clearAllMocks, unmockkAll].
    maxParallelForks = 1
}

tasks.withType<ShadowJar> {
    // resolving missing/overridden content https://stackoverflow.com/questions/56152800/error-with-building-fatjar-using-kotlin-dsl
    mergeServiceFiles()
    manifest {
        attributes["Main-Class"] = "hero.${project.name.lowercase()}.MainKt"
        //  multi-release class files are not being pcked up from META-INF/versions/* if not set to true
        //  https://stackoverflow.com/questions/52953483/logmanager-getlogger-is-unable-to-determine-class-name-on-java-11
        attributes["Multi-Release"] = "true"
    }
    isZip64 = true
    archiveFileName.set("app.jar")
    destinationDirectory.set(file("build/shadow"))
    // https://github.com/johnrengelman/shadow/issues/107
    isZip64 = true
    transform(Log4j2PluginsCacheFileTransformer())
}

ktlint {
    version.set("1.6.0")
}

detekt {
    ignoreFailures = true
    basePath = rootDir.parent.toString()
}

val reportMerge by tasks.registering(io.gitlab.arturbosch.detekt.report.ReportMergeTask::class) {
    output.set(rootProject.layout.buildDirectory.file("reports/detekt/merge.xml"))
}

tasks.withType<Detekt> {
    if (project.path == coreModulePath) {
        enabled = false
    }
    reports {
        xml.required.set(true)
        custom {
            reportId = "DetektGitlabReport"
            // This tells detekt, where it should write the report to,
            // you have to specify this file in the gitlab pipeline config.
            outputLocation.set(file(rootProject.layout.buildDirectory.file("reports/detekt/gitlab.json")))
        }
    }
    finalizedBy(reportMerge)
    reportMerge.configure {
        input.from(xmlReportFile)
    }
}

val afterTask by tasks.register("afterTaskResults") {
    outputs.upToDateWhen { false }
    doLast {
        val testResultsDir = file(rootProject.layout.buildDirectory.dir("test-results/test"))
        if (!testResultsDir.exists()) {
            testResultsDir.mkdirs()
            file("$testResultsDir/TEST-${project.name}.xml")
                .writeText("<testsuites></testsuites>")
        }
    }
}

tasks.test {
    finalizedBy(afterTask)
}
