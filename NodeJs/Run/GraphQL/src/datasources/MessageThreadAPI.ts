import { ServiceDataSource } from './ServiceDataSource'
import { Environment } from '../common/environment'
import { DataSourceConfig } from '@apollo/datasource-rest'
import { PaginationModel } from '../models/pagination'
import { MessageModel, MessageThreadModel } from '../models/message-thread'
import {
    MessageThreadDetailsResponse,
    MessageThreadDto,
    PagedMessageThreadsResponse,
    PagedPostResponse,
    PostAssetInput,
    PostResponse,
    UpdateWelcomeMessageRequest,
} from '../generated/api'
import { GraphQLError } from 'graphql/error'
import { mapToMessage, mapToUser } from './common-mappers'
import { PaginationParams, paginationParamsToQueryParams } from './common-utils'

export class MessageThreadAPI extends ServiceDataSource {
    constructor(environment: Environment, cookies?: string, config?: DataSourceConfig) {
        super(environment, 'api', cookies, config)
    }

    async getMessageThreads(
        paginationParams: PaginationParams
    ): Promise<{ messageThreads: MessageThreadModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        const response = await this.get<PagedMessageThreadsResponse>(`/v2/message-threads`, { params })
        const messageThreads = response.content.map((messageThread) => {
            const lastMessage = messageThread.lastMessage
            return {
                id: messageThread.id,
                participants: messageThread.participants.map((user) => mapToUser(user)),
                participantIds: messageThread.participantIds,
                createdAt: messageThread.createdAt,
                canMessage: messageThread.canMessage,
                seenAt: messageThread.seenAt,
                checkedAt: messageThread.checkedAt,
                lastMessageAt: messageThread.lastMessageAt,
                lastMessage: mapToMessage(lastMessage),
            }
        })

        return {
            messageThreads,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
            },
        }
    }

    async getMessageThread(messageThreadId: string): Promise<MessageThreadModel> {
        if (messageThreadId.length == 0) {
            throw new GraphQLError('Message thread id must not be empty', { extensions: { code: 'BAD_REQUEST' } })
        }

        const response = await this.get<MessageThreadDetailsResponse>(`/v2/message-threads/${messageThreadId}`)
        return {
            id: response.id,
            participants: response.participants.map((user) => mapToUser(user)),
            participantIds: response.participantIds,
            createdAt: response.createdAt,
            canMessage: response.canPost,
            seenAt: response.seenAt,
            checkedAt: response.checkedAt,
            lastMessageId: response.lastMessageId,
            lastMessageAt: response.lastMessageAt,
        }
    }

    async getMessages(
        messageThreadId: string,
        paginationParams: PaginationParams
    ): Promise<{ messages: MessageModel[]; pagination: PaginationModel }> {
        const params = paginationParamsToQueryParams(paginationParams)
        const response = await this.get<PagedPostResponse>(`/v2/message-threads/${messageThreadId}/messages`, {
            params,
        })

        const messages = response.content.map(mapToMessage)

        return {
            messages,
            pagination: {
                hasNextPage: response.hasNext,
                endCursor: response.afterCursor,
                startCursor: response.beforeCursor,
            },
        }
    }

    async postMessages({ messageThreadId, text }: { messageThreadId: string; text: string }): Promise<MessageModel> {
        const body = { text }
        const response = await this.post<PostResponse>(`/v2/message-threads/${messageThreadId}/messages`, {
            body,
        })

        return mapToMessage(response)
    }

    async postMessageThreads(userId: string, participantIds: string[]): Promise<MessageThreadModel> {
        const body: MessageThreadDto = {
            type: 'message-thread',
            attributes: {
                commonCreators: [],
                deleted: false,
                archived: false,
            },
            relationships: {
                users: [{ id: userId, type: 'user' }].concat(participantIds.map((id) => ({ id, type: 'user' }))),
            },
        }

        const response = await this.post<MessageThreadDto>(`/v1/message-threads`, {
            body,
        })

        if (response.id == null) {
            throw new Error()
        }

        return {
            id: response.id,
            participants: [],
            participantIds: response.relationships.users.map((user) => user.id),
            createdAt: response.attributes.createdAt,
            seenAt: response.attributes.seenAt,
            checkedAt: response.attributes.checkedAt,
            lastMessageAt: response.attributes.lastMessageAt,
            lastMessage: undefined,
            canMessage: response.attributes.canPost ?? false,
        }
    }

    async markAllSeen(userId: string): Promise<void> {
        await this.post(`/v1/users/${userId}/mark-message-threads-seen`)
    }

    async getMessage(messageId: string): Promise<MessageModel> {
        const response = await this.get<PostResponse>(`/v1/messages/${messageId}`)

        return mapToMessage(response)
    }

    async getWelcomeMessage(userId: string): Promise<MessageModel> {
        const response = await this.get<PostResponse>(`/v1/users/${userId}/welcome-messages`)

        return mapToMessage(response)
    }

    async updateWelcomeMessage(userId: string, text: string, assets: PostAssetInput[]): Promise<MessageModel> {
        const body: UpdateWelcomeMessageRequest = {
            text,
            assets,
        }
        const response = await this.put<PostResponse>(`/v1/users/${userId}/welcome-messages`, { body })

        return mapToMessage(response)
    }

    async patchMessageThread(messageThreadId: string, checkedAt: string | undefined): Promise<void> {
        const body: MessageThreadDto = {
            id: messageThreadId,
            attributes: {
                commonCreators: [],
                deleted: false,
                archived: false,
                checkedAt,
            },
            relationships: {
                users: [],
            },
            type: 'message-thread',
        }
        await this.patch<MessageThreadDto>(`/v1/message-threads/${messageThreadId}`, { body })
    }
}
