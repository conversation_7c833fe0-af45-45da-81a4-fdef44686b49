/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ImageAsset } from './ImageAsset';
import type { UserResponse } from './UserResponse';
export type CommunityResponse = {
    id: string;
    name: string;
    description: string;
    slug: string;
    ownerId: string;
    owner: UserResponse;
    membersCount: number;
    image?: ImageAsset;
    createdAt: string;
    isVerified: boolean;
    isMember: boolean;
    threadsCount: number;
    slugEditableAfter: string;
};

